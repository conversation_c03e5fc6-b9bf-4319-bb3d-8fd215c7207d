<template>
  <a-modal :open="modalVisible" title="设置API Key" :width="400" @ok="handleOk" @cancel="handleCancel">
    <a-form :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="API Key" name="apiKey" :rules="[{ required: true, message: '请输入API Key!' }]">
        <a-input v-model:value="formState.apiKey" placeholder="请输入API Key" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
  import { ref, watch, reactive } from 'vue';
  import { getModelApiKey } from '/@/enums/httpEnum';

  const props = defineProps<{
    modelKey: string;
    visible: boolean;
  }>();

  const emit = defineEmits<{
    (e: 'update:visible', visible: boolean): void;
    (e: 'save', data: { modelKey: string; apiKey: string }): void;
  }>();

  interface FormState {
    apiKey: string;
  }

  const formState = reactive<FormState>({ apiKey: '' });
  const modalVisible = ref<boolean>(false);

  watch(
    () => props.visible,
    (val: boolean) => {
      modalVisible.value = val;
      if (val) {
        formState.apiKey = getModelApiKey(props.modelKey);
      }
    },
    { immediate: true }
  );

  watch(
    () => modalVisible.value,
    (val: boolean) => {
      emit('update:visible', val);
    }
  );

  function handleOk() {
    if (!formState.apiKey) return;
    emit('save', { modelKey: props.modelKey, apiKey: formState.apiKey });
    modalVisible.value = false;
  }

  function handleCancel() {
    modalVisible.value = false;
  }
</script>
<style scoped></style>
