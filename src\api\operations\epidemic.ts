import { defHttp } from '/@/utils/http/axios';

// 健康码状态
export type HealthCodeStatus = 'green' | 'yellow' | 'red';

// 疫苗接种状态
export type VaccineStatus = 'none' | 'partial' | 'full' | 'booster';

// 人员健康信息
export interface HealthInfo {
  id: number;
  personId: string;
  name: string;
  phone: string;
  department?: string;
  healthCode: HealthCodeStatus;
  temperature: number;
  vaccineStatus: VaccineStatus;
  vaccineDate?: string;
  nucleicTestDate?: string;
  nucleicTestResult?: 'negative' | 'positive' | 'pending';
  riskArea?: string;
  travelHistory?: string[];
  symptoms?: string[];
  checkTime: string;
  checkLocation: string;
  isEmployee: boolean;
}

// 疫情防控统计
export interface EpidemicStats {
  totalChecked: number;
  greenCodeCount: number;
  yellowCodeCount: number;
  redCodeCount: number;
  abnormalTemperature: number;
  vaccinatedCount: number;
  nucleicTestCount: number;
  riskPersonCount: number;
  todayEntries: number;
  alertCount: number;
}

// 防控措施配置
export interface ControlMeasures {
  temperatureThreshold: number;
  requireHealthCode: boolean;
  requireVaccine: boolean;
  requireNucleicTest: boolean;
  nucleicTestValidDays: number;
  restrictedAreas: string[];
  emergencyContacts: Array<{
    name: string;
    phone: string;
    role: string;
  }>;
}

// 风险区域信息
export interface RiskArea {
  id: number;
  areaCode: string;
  areaName: string;
  province: string;
  city: string;
  district: string;
  riskLevel: 'low' | 'medium' | 'high';
  updateTime: string;
}

// 查询参数
export interface EpidemicQueryParams {
  pageNo?: number;
  pageSize?: number;
  name?: string;
  phone?: string;
  healthCode?: HealthCodeStatus;
  department?: string;
  checkTime?: string[];
  isEmployee?: boolean;
}

enum Api {
  HEALTH_CHECK = '/operations/epidemic/health-check',
  HEALTH_LIST = '/operations/epidemic/health-list',
  EPIDEMIC_STATS = '/operations/epidemic/stats',
  CONTROL_MEASURES = '/operations/epidemic/measures',
  RISK_AREAS = '/operations/epidemic/risk-areas',
  TEMPERATURE_CHECK = '/operations/epidemic/temperature',
  HEALTH_CODE_VERIFY = '/operations/epidemic/health-code',
  VACCINE_RECORD = '/operations/epidemic/vaccine',
  NUCLEIC_TEST = '/operations/epidemic/nucleic-test',
  ALERT_HANDLE = '/operations/epidemic/alert',
}

/**
 * 健康检查登记
 */
export function healthCheck(data: {
  personId: string;
  name: string;
  phone: string;
  department?: string;
  temperature: number;
  healthCode: HealthCodeStatus;
  vaccineStatus: VaccineStatus;
  nucleicTestDate?: string;
  nucleicTestResult?: string;
  travelHistory?: string[];
  symptoms?: string[];
  checkLocation: string;
  isEmployee: boolean;
}) {
  return defHttp.post({
    url: Api.HEALTH_CHECK,
    data,
  });
}

/**
 * 获取健康检查记录列表
 */
export function getHealthList(params: EpidemicQueryParams) {
  return defHttp.get<{
    records: HealthInfo[];
    total: number;
    current: number;
    size: number;
  }>({
    url: Api.HEALTH_LIST,
    params,
  });
}

/**
 * 获取疫情防控统计数据
 */
export function getEpidemicStats() {
  return defHttp.get<EpidemicStats>({
    url: Api.EPIDEMIC_STATS,
  });
}

/**
 * 获取防控措施配置
 */
export function getControlMeasures() {
  return defHttp.get<ControlMeasures>({
    url: Api.CONTROL_MEASURES,
  });
}

/**
 * 更新防控措施配置
 */
export function updateControlMeasures(data: ControlMeasures) {
  return defHttp.put({
    url: Api.CONTROL_MEASURES,
    data,
  });
}

/**
 * 获取风险区域列表
 */
export function getRiskAreas() {
  return defHttp.get<RiskArea[]>({
    url: Api.RISK_AREAS,
  });
}

/**
 * 体温检测
 */
export function temperatureCheck(data: {
  personId: string;
  temperature: number;
  checkLocation: string;
}) {
  return defHttp.post({
    url: Api.TEMPERATURE_CHECK,
    data,
  });
}

/**
 * 健康码验证
 */
export function verifyHealthCode(data: {
  personId: string;
  healthCode: HealthCodeStatus;
  qrCodeData?: string;
}) {
  return defHttp.post({
    url: Api.HEALTH_CODE_VERIFY,
    data,
  });
}

/**
 * 疫苗接种记录
 */
export function recordVaccine(data: {
  personId: string;
  vaccineType: string;
  vaccineDate: string;
  vaccineLocation: string;
  dose: number;
}) {
  return defHttp.post({
    url: Api.VACCINE_RECORD,
    data,
  });
}

/**
 * 核酸检测记录
 */
export function recordNucleicTest(data: {
  personId: string;
  testDate: string;
  testLocation: string;
  result: 'negative' | 'positive' | 'pending';
  reportNumber?: string;
}) {
  return defHttp.post({
    url: Api.NUCLEIC_TEST,
    data,
  });
}

/**
 * 处理疫情告警
 */
export function handleEpidemicAlert(data: {
  alertId: number;
  action: 'isolate' | 'test' | 'monitor' | 'clear';
  remark?: string;
}) {
  return defHttp.post({
    url: Api.ALERT_HANDLE,
    data,
  });
}
