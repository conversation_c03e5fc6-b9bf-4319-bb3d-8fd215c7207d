import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetRealtime = '/electricityMeter/getReal',
  GetHistory = '/electricityMeter/getHistory',
  GetTop = '/electricityMeter/top', // 新增的TOP10接口
}

// 电表历史数据查询参数接口
export interface ElectricityHistoryParams {
  floorInfo?: string; // 楼层信息
  day?: number; // 查询天数
}

/**
 * @description: 获取电表实时数据
 */
export function getElectricityRealtime(floorInfo?: string) {
  return defHttp.get<any[]>({
    url: Api.GetRealtime,
    params: floorInfo ? { floorInfo } : undefined,
  });
}

/**
 * @description: 获取电表历史数据
 * @param params 查询参数，包含 floorInfo（楼层信息）和 day（查询天数）
 */
export function getElectricityHistory(params?: ElectricityHistoryParams): Promise<Record<string, any[]>> {
  // 过滤掉 undefined 值的参数
  const filteredParams = params ? Object.fromEntries(Object.entries(params).filter(([_, value]) => value !== undefined)) : undefined;

  return defHttp.get<Record<string, any[]>>({
    url: Api.GetHistory,
    params: filteredParams,
  });
}

/**
 * @description: 获取电表能耗排行Top10
 */
export function getElectricityTop() {
  return defHttp.get<any[]>({ url: Api.GetTop });
}
