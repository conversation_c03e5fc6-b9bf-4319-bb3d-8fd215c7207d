﻿/**
 * 动环实时数据全局变量
 */
const realTimeData = {
    // 当前页码
    currentPage: 1,
    // 每页显示数量
    pageSize: 10,
    // 总页数
    totalPages: 0,
    // 总记录数
    totalRecords: 0,
    // 所有数据
    allData: [],
    // 当前页数据
    currentPageData: []
};

$(function () {
    console.log('%c子应用jQuery文档加载完成', 'background: #673AB7; color: white; padding: 4px 8px; border-radius: 4px;');

    try {
        console.log('%c初始化图表', 'background: #009688; color: white; padding: 2px 4px; border-radius: 2px;');
        // 移除原有的设备运行指标图表初始化
        // echarts_1();
        // echarts_2();
        // echarts_3();
        // echarts_4();
        // echarts_5();
        // echarts_6();

        // 初始化动环实时数据
        initRealTimeData();

        // 初始化其他图表
        echarts_7();
        echarts_8();
        // 移除能耗趋势分析图表初始化，因为该区域已被替换为3D模型
        // initAnalysisChart();

        console.log('%c图表初始化完成', 'background: #009688; color: white; padding: 2px 4px; border-radius: 2px;');

        // 初始化荆门市地图
        console.log('%c初始化荆门市地图', 'background: #9C27B0; color: white; padding: 2px 4px; border-radius: 2px;');
        try {
            // 确保echarts库已加载
            if (typeof echarts !== 'undefined' && typeof window.jingmenMap !== 'undefined') {
                // 初始化荆门市地图
                window.jingmenMapInstance = window.jingmenMap.init('map-container');
                console.log('%c荆门市地图初始化成功', 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;');
            } else {
                console.error('%c初始化荆门市地图失败: echarts库未加载', 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
                // 显示错误消息
                $('#map-container').html('<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #ff5252; font-size: 14px; text-align: center;">荆门市地图加载失败: echarts库未加载</div>');
            }
        } catch (mapError) {
            console.error('%c初始化荆门市地图时出错:', 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;', mapError);
            // 显示错误消息
            $('#map-container').html('<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #ff5252; font-size: 14px; text-align: center;">荆门市地图加载失败: ' + mapError.message + '</div>');
        }

        // 检查API工具是否已加载
        console.log('%c检查API工具是否已加载', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
        console.log('window.portalApi:', window.portalApi);

        // 如果存在全局API加载函数，调用它
        if (typeof window.loadPortalApiData === 'function') {
            console.log('%c调用全局API加载函数', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
            // 延迟执行API调用，确保API工具已加载
            setTimeout(function() {
                window.loadPortalApiData();
            }, 500);
        } else {
            console.log('%c全局API加载函数未定义，跳过API加载', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
        }
    } catch (error) {
        console.error('%c初始化过程中出错:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);
    }

function echarts_1() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('echart1'));
	option = {
    title: {
        text: '78%',
        x: 'center',
        y: 'center',
        textStyle: {
            fontWeight: 'normal',
            color: '#0580f2',
            fontSize: '24'
        }
    },
    color: ['rgba(176, 212, 251, .1)'],
    series: [{
        name: 'Line 1',
        type: 'pie',
        clockWise: true,
            radius: ['75%', '85%'],
        itemStyle: {
            normal: {
                label: {show: false},
                labelLine: {show: false},
            }
        },

        hoverAnimation: false,
        data: [{
            value: 78,
            name: '01',
            itemStyle: {
                normal: {
                    color: { // 完成的圆环的颜色
                        colorStops: [{
                            offset: 0,
                            color: '#00cefc' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: '#367bec' // 100% 处的颜色
                        }]
                    },
                    label: {show: false},
                    labelLine: {show: false}
                }
            }
        }, {
            name: '86',
            value: 22
        }]

    }]

}
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize",function(){
            myChart.resize();
        });
    }
function echarts_2() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('echart2'));
	option = {
    title: {
        text: '65%',
        x: 'center',
        y: 'center',
        textStyle: {
            fontWeight: 'normal',
            color: '#0580f2',
          fontSize: '24'
        }
    },
    color: ['rgba(176, 212, 251, .1)'],
    series: [{
        name: 'Line 1',
        type: 'pie',
        clockWise: true,
        radius: ['75%', '85%'],
        itemStyle: {
            normal: {
                label: {show: false},
                labelLine: {show: false},
            }
        },

        hoverAnimation: false,
        data: [{
            value: 65,
            name: '01',
            itemStyle: {
                normal: {
                    color: { // 完成的圆环的颜色
                        colorStops: [{
                            offset: 0,
                            color: '#00cefc' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: '#367bec' // 100% 处的颜色
                        }]
                    },
                    label: {show: false},
                    labelLine: {show: false}
                }
            }
        }, {
            name: '78',
            value: 35
        }]

    }]

}
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize",function(){
            myChart.resize();
        });
    }
function echarts_3() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('echart3'));
	option = {
    title: {
        text: '82%',
        x: 'center',
        y: 'center',
        textStyle: {
            fontWeight: 'normal',
            color: '#0580f2',
            fontSize: '24'
        }
    },
    color: ['rgba(176, 212, 251, .1)'],
    series: [{
        name: 'Line 1',
        type: 'pie',
        clockWise: true,
         radius: ['75%', '85%'],
        itemStyle: {
            normal: {
                label: {show: false},
                labelLine: {show: false},
            }
        },

        hoverAnimation: false,
        data: [{
            value: 82,
            name: '01',
            itemStyle: {
                normal: {
                    color: { // 完成的圆环的颜色
                        colorStops: [{
                            offset: 0,
                            color: '#00cefc' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: '#367bec' // 100% 处的颜色
                        }]
                    },
                    label: {show: false},
                    labelLine: {show: false}
                }
            }
        }, {
            name: '02',
            value: 18
        }]

    }]

}
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize",function(){
            myChart.resize();
        });
    }
function echarts_4() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('echart4'));
	option = {
    title: {
        text: '85%',
        x: 'center',
        y: 'center',
        textStyle: {
            fontWeight: 'normal',
            color: '#0580f2',
            fontSize: '24'
        }
    },
    color: ['rgba(176, 212, 251, .1)'],
    series: [{
        name: 'Line 1',
        type: 'pie',
        clockWise: true,
            radius: ['75%', '85%'],
        itemStyle: {
            normal: {
                label: {show: false},
                labelLine: {show: false},
            }
        },

        hoverAnimation: false,
        data: [{
            value: 85,
            name: '01',
            itemStyle: {
                normal: {
                    color: { // 完成的圆环的颜色
                        colorStops: [{
                            offset: 0,
                            color: '#00cefc' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: '#367bec' // 100% 处的颜色
                        }]
                    },
                    label: {show: false},
                    labelLine: {show: false}
                }
            }
        }, {
            name: '02',
            value: 15
        }]

    }]

}
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize",function(){
            myChart.resize();
        });
    }
function echarts_5() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('echart5'));
	option = {
    title: {
        text: '72%',
        x: 'center',
        y: 'center',
        textStyle: {
            fontWeight: 'normal',
            color: '#0580f2',
          fontSize: '24'
        }
    },
    color: ['rgba(176, 212, 251, .1)'],
    series: [{
        name: 'Line 1',
        type: 'pie',
        clockWise: true,
        radius: ['75%', '85%'],
        itemStyle: {
            normal: {
                label: {show: false},
                labelLine: {show: false},
            }
        },

        hoverAnimation: false,
        data: [{
            value: 72,
            name: '01',
            itemStyle: {
                normal: {
                    color: { // 完成的圆环的颜色
                        colorStops: [{
                            offset: 0,
                            color: '#00cefc' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: '#367bec' // 100% 处的颜色
                        }]
                    },
                    label: {show: false},
                    labelLine: {show: false}
                }
            }
        }, {
            name: '02',
            value: 28
        }]

    }]

}
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize",function(){
            myChart.resize();
        });
    }
function echarts_6() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('echart6'));
	option = {
    title: {
        text: '1.42',
        x: 'center',
        y: 'center',
        textStyle: {
            fontWeight: 'normal',
            color: '#0580f2',
            fontSize: '24'
        }
    },
    color: ['rgba(176, 212, 251, .1)'],
    series: [{
        name: 'Line 1',
        type: 'pie',
        clockWise: true,
         radius: ['75%', '85%'],
        itemStyle: {
            normal: {
                label: {show: false},
                labelLine: {show: false},
            }
        },

        hoverAnimation: false,
        data: [{
            value: 71,
            name: '01',
            itemStyle: {
                normal: {
                    color: { // 完成的圆环的颜色
                        colorStops: [{
                            offset: 0,
                            color: '#00cefc' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: '#367bec' // 100% 处的颜色
                        }]
                    },
                    label: {show: false},
                    labelLine: {show: false}
                }
            }
        }, {
            name: '02',
            value: 29
        }]

    }]

}
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize",function(){
            myChart.resize();
        });
    }
function echarts_7() {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(document.getElementById('echart7'));

    // 测点类型ID到名称的映射
    var typeNameMap = {
        1: '数字输出量(DO/遥控)',
        2: '模拟输出量(AO/遥调)',
        3: '模拟输入量(AI/遥测)',
        4: '数字输入量(DI/遥信)'
    };

    // 默认配置
    var option = {
        title: {
            text: '测点数排名',
            left: 'center',
            top: 0,
            textStyle: {
                color: '#fff',
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                var param = params[0];
                return param.name + '<br/>' +
                       '数量: ' + param.value + '<br/>' +
                       '占比: ' + param.data.percentage + '%';
            }
        },
        grid: {
            left: '3%',
            top: '40px',
            right: '3%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: [],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0.2)'
                }
            },
            axisLabel: {
                color: '#fff',
                fontSize: 12,
                interval: 0,
                rotate: 30
            }
        },
        yAxis: {
            type: 'value',
            name: '数量',
            nameTextStyle: {
                color: '#fff',
                fontSize: 12
            },
            axisLine: {
                show: false,
            },
            axisLabel: {
                color: '#fff',
                fontSize: 12
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0.1)'
                }
            }
        },
        series: [{
            name: '测点数量',
            type: 'bar',
            barWidth: '40%',
            itemStyle: {
                normal: {
                    barBorderRadius: [5, 5, 0, 0],
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        offset: 0,
                        color: '#01fdcc'
                    }, {
                        offset: 0.8,
                        color: '#11a1d8'
                    }], false)
                }
            },
            data: []
        }]
    };

    // 检查API是否可用
    if (window.portalApi && typeof window.portalApi.getDeviceTypeInfo === 'function') {
        console.log('%c调用getDeviceTypeInfo API获取测点数排名数据', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');

        // 调用API获取数据
        window.portalApi.getDeviceTypeInfo()
            .then(function(response) {
                console.log('%cgetDeviceTypeInfo API返回结果:', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;', response);

                if (response && response.success && response.result && Array.isArray(response.result)) {
                    // 提取数据
                    var chartData = response.result;

                    // 更新图表数据
                    updatePointRankingChart(chartData);
                } else {
                    console.warn('%c响应格式不符合预期', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
                    myChart.setOption(option);
                }
            })
            .catch(function(error) {
                console.error('%cgetDeviceTypeInfo API调用失败:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);
                myChart.setOption(option);
            });
    } else {
        console.error('%cAPI不可用', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;');
        myChart.setOption(option);
    }

    // 更新图表数据的函数
    function updatePointRankingChart(data) {
        // 处理数据，将type数字ID转换为可读名称
        var processedData = data.map(function(item) {
            return {
                type: item.type,
                typeName: typeNameMap[item.type] || '类型' + item.type,
                number: item.number || 0
            };
        });

        // 计算总数
        var totalPoints = processedData.reduce(function(sum, item) {
            return sum + item.number;
        }, 0);

        // 按数量从高到低排序
        processedData.sort(function(a, b) {
            return b.number - a.number;
        });

        // 格式化图表数据，并计算百分比
        var xAxisData = [];
        var seriesData = [];

        processedData.forEach(function(item) {
            var percentage = ((item.number / totalPoints) * 100).toFixed(1);
            xAxisData.push(item.typeName);
            seriesData.push({
                value: item.number,
                percentage: percentage,
                itemStyle: {
                    // 为每种类型设置不同的颜色
                    color: getColorByType(item.type)
                }
            });
        });

        // 更新图表配置
        option.xAxis.data = xAxisData;
        option.series[0].data = seriesData;

        // 更新图表
        myChart.setOption(option);

        console.log('%c测点数排名图表更新完成', 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;');
    }

    // 根据测点类型获取颜色
    function getColorByType(type) {
        var colors = {
            1: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: '#01fdcc'},
                {offset: 1, color: '#11a1d8'}
            ]),
            2: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: '#FFD700'},
                {offset: 1, color: '#FFA500'}
            ]),
            3: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: '#00BFFF'},
                {offset: 1, color: '#1E90FF'}
            ]),
            4: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: '#FF69B4'},
                {offset: 1, color: '#FF1493'}
            ])
        };

        return colors[type] || new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {offset: 0, color: '#01fdcc'},
            {offset: 1, color: '#11a1d8'}
        ]);
    }

    // 窗口大小变化时，重新调整图表大小
    window.addEventListener("resize", function() {
        myChart.resize();
    });
}
function echarts_8() {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(document.getElementById('echart8'));

    // 默认配置
    var option = {
        color: ['#ec704a', '#2e4453', '#249cf9', '#fdb629', '#4b5cc4', '#f47983', '#8d4bbb', '#6635EF', '#FFAFDA'],
        tooltip: {
            trigger: 'item',
            formatter: "{a} <br/>{b}: {c} ({d}%)"
        },
        legend: {
            orient: 'vertical',
            right: 0,
            y: 'center',
            itemWidth: 12,
            itemHeight: 12,
            align: 'left',
            textStyle: {
                fontSize: 12,
                color: '#fff'
            },
            data: []
        },
        series: [
            {
                name: '测点数占比',
                type: 'pie',
                center: ['50%', '50%'],
                radius: ['20%', '50%'],
                label: {
                    show: true,
                    position: 'outside',
                    formatter: function(params) {
                        // 显示名称和百分比
                        return params.name + ': ' + params.percent.toFixed(2) + '%';
                    },
                    fontSize: 12,
                    color: '#fff',
                    fontWeight: 'bold'
                },
                labelLine: {
                    normal: {
                        show: true,
                        length: 2,
                        length2: 5,
                        lineStyle: {
                            width: 1
                        }
                    }
                },
                // 移除 roseType 设置，使用标准饼图
                // roseType: 'area',
                data: []
            }
        ]
    };

    // 检查API是否可用
    if (window.portalApi && typeof window.portalApi.getDeviceTypeInfo === 'function') {
        console.log('%c调用getDeviceTypeInfo API获取测点数占比数据', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');

        // 调用API获取数据
        window.portalApi.getDeviceTypeInfo()
            .then(function(response) {
                console.log('%cgetDeviceTypeInfo API返回结果:', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;', response);

                if (response && response.success && response.result && Array.isArray(response.result)) {
                    // 提取数据
                    var chartData = response.result;

                    // 更新图表数据
                    updateDeviceTypeChart(chartData);
                } else {
                    console.warn('%c响应格式不符合预期，使用模拟数据', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
                    // 使用模拟数据（符合API返回格式）
                    var mockData = [
                        { type: 1, number: 220 },
                        { type: 2, number: 190 },
                        { type: 3, number: 210 },
                        { type: 4, number: 180 },
                        { type: 5, number: 200 }
                    ];
                    updateDeviceTypeChart(mockData);
                }
            })
            .catch(function(error) {
                console.error('%cgetDeviceTypeInfo API调用失败:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);

                // 使用模拟数据（符合API返回格式）
                var mockData = [
                    { type: 1, number: 220 },
                    { type: 2, number: 190 },
                    { type: 3, number: 210 },
                    { type: 4, number: 180 },
                    { type: 5, number: 200 }
                ];
                updateDeviceTypeChart(mockData);
            });
    } else {
        console.error('%cAPI不可用，使用模拟数据', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;');

        // 使用模拟数据（符合API返回格式）
        var mockData = [
            { type: 1, number: 220 },
            { type: 2, number: 190 },
            { type: 3, number: 210 },
            { type: 4, number: 180 },
            { type: 5, number: 200 }
        ];
        updateDeviceTypeChart(mockData);
    }

    // 更新图表数据的函数
    function updateDeviceTypeChart(data) {
        // 测点类型ID到名称的映射
        var typeNameMap = {
            1: 'DO',  // 数字输出量，遥控
            2: 'AO',  // 模拟输出量，遥调
            3: 'AI',  // 模拟输入量，遥测
            4: 'DI',  // 数字输入量，遥信
            5: '其他'
        };

        // 处理数据，将type数字ID转换为可读名称
        var processedData = data.map(function(item) {
            return {
                type: typeNameMap[item.type] || '类型' + item.type,  // 如果映射中没有对应的名称，则使用"类型+ID"
                number: item.number || 0
            };
        });

        // 提取类型名称用于图例
        var legendData = processedData.map(function(item) {
            return item.type;
        });

        // 计算总数
        var totalPoints = processedData.reduce(function(sum, item) {
            return sum + item.number;
        }, 0);

        // 格式化图表数据，并计算百分比
        var seriesData = processedData.map(function(item) {
            var percentage = ((item.number / totalPoints) * 100).toFixed(2);
            return {
                value: item.number,
                name: item.type,
                percentage: percentage // 存储百分比，方便后续使用
            };
        });

        // 更新图表配置
        option.legend.data = legendData;
        option.series[0].data = seriesData;

        // 添加饼图的特殊配置，确保大比例数据正确显示
        option.series[0].emphasis = {
            itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        };

        // 更新图表
        myChart.setOption(option);

        // 总测点数已经在前面计算过了，不需要重复计算

        // 更新UI中的总测点数 - 从资源统计中获取
        var dhNum = $('.stats-content:contains("测点数量") em').text();
        if (dhNum && !isNaN(parseInt(dhNum))) {
            $('#totalPoints').html(dhNum + '<i>个</i>');
        } else {
            $('#totalPoints').html(totalPoints + '<i>个</i>');
        }

        // 更新各类测点的数量和占比
        var pointTypeStatsHtml = '';
        seriesData.forEach(function(item) {
            // 格式化百分比，确保不会太长
            var percentage = parseFloat(item.percentage).toFixed(1);
            pointTypeStatsHtml += `
            <div class="point-type-item">
                <span class="point-type-name">${item.name}</span>
                <span class="point-type-value">${item.value}个 (${percentage}%)</span>
            </div>
            `;
        });

        // 更新到DOM
        $('#pointTypeStats').html(pointTypeStatsHtml);

        console.log('%c测点数占比图表更新完成', 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;');
    }

    // 窗口大小变化时，重新调整图表大小
    window.addEventListener("resize", function() {
        myChart.resize();
    });
}

/**
 * 初始化动环实时数据
 * 从API获取数据并以现代化的方式展示
 */
function initRealTimeData() {
    console.log('%c初始化动环实时数据', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');

    // 获取容器
    const container = $('#realTimeDataContainer');

    // 显示加载中
    container.html('<div class="loading-indicator">加载中...</div>');

    // 绑定分页按钮点击事件
    $('#realTimeDataPrevPage').on('click', function() {
        if (realTimeData.currentPage > 1) {
            realTimeData.currentPage--;
            // 重新加载数据而不是渲染缓存数据
            loadRealTimeData();
        }
    });

    $('#realTimeDataNextPage').on('click', function() {
        if (realTimeData.currentPage < realTimeData.totalPages) {
            realTimeData.currentPage++;
            // 重新加载数据而不是渲染缓存数据
            loadRealTimeData();
        }
    });

    // 加载数据
    loadRealTimeData();

    // 设置定时刷新（每60秒刷新一次）
    setInterval(loadRealTimeData, 60000);
}

/**
 * 加载动环实时数据
 */
function loadRealTimeData() {
    console.log('%c加载动环实时数据', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');

    // 获取容器
    const container = $('#realTimeDataContainer');

    // 检查API是否可用
    if (window.portalApi && typeof window.portalApi.getRealTimeData === 'function') {
        // 调用API获取分页数据，设置type=3，使用当前页码和页面大小
        const params = {
            type: 3,
            pageNo: realTimeData.currentPage,
            pageSize: realTimeData.pageSize
        };
        
        window.portalApi.getRealTimeData(params)
            .then(function(response) {
                console.log('%cgetRealTimeData API返回结果:', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;', response);

                // 处理数据
                processRealTimeDataPaginated(response);
            })
            .catch(function(error) {
                console.error('%cgetRealTimeData API调用失败:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);

                // 显示错误信息
                container.html('<div class="loading-indicator">获取动环实时数据失败</div>');
            });
    } else {
        console.error('%cAPI不可用，无法获取动环实时数据', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;');

        // 显示错误信息
        container.html('<div class="loading-indicator">API不可用，无法获取动环实时数据</div>');
    }
}

/**
 * 处理分页动环实时数据
 * @param {Object} response - API响应数据
 */
function processRealTimeDataPaginated(response) {
    // 检查响应是否有效
    if (response && response.success) {
        console.log('%c处理分页动环实时数据', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;');

        // 获取分页数据
        let result = response.result;
        if (result && typeof result === 'object') {
            // 处理分页格式的数据
            if (result.records && Array.isArray(result.records)) {
                realTimeData.currentPageData = result.records;
                realTimeData.totalRecords = result.total || 0;
                realTimeData.currentPage = result.current || realTimeData.currentPage;
                realTimeData.totalPages = result.pages || Math.ceil(realTimeData.totalRecords / realTimeData.pageSize);
                
                console.log('%c分页数据:', 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;', 
                    `当前页: ${realTimeData.currentPage}/${realTimeData.totalPages}, 本页: ${realTimeData.currentPageData.length}条, 总计: ${realTimeData.totalRecords}条`);
            } else if (Array.isArray(result)) {
                // 如果直接是数组，说明不是分页数据
                realTimeData.currentPageData = result;
                realTimeData.totalRecords = result.length;
                realTimeData.totalPages = 1;
                realTimeData.currentPage = 1;
                
                console.log('%c非分页数据:', 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;', 
                    `总计: ${realTimeData.totalRecords}条`);
            } else {
                console.warn('%c响应中没有找到有效的数据记录', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
                $('#realTimeDataContainer').html('<div class="loading-indicator">暂无动环实时数据</div>');
                return;
            }
        } else {
            console.warn('%c响应结果格式异常', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
            $('#realTimeDataContainer').html('<div class="loading-indicator">暂无动环实时数据</div>');
            return;
        }

        // 更新分页信息
        updateRealTimeDataPagination();

        // 直接渲染分页返回的数据
        renderRealTimeDataTable();
    } else {
        console.warn('%c响应格式不符合预期，无法处理动环实时数据', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
        console.warn('响应数据:', response);

        // 显示错误信息
        $('#realTimeDataContainer').html('<div class="loading-indicator">获取动环实时数据失败</div>');
    }
}

/**
 * 更新分页信息
 */
function updateRealTimeDataPagination() {
    // 更新页码信息
    $('#realTimeDataCurrentPage').text(realTimeData.currentPage);
    $('#realTimeDataTotalPages').text(realTimeData.totalPages);

    // 更新按钮状态
    $('#realTimeDataPrevPage').prop('disabled', realTimeData.currentPage <= 1);
    $('#realTimeDataNextPage').prop('disabled', realTimeData.currentPage >= realTimeData.totalPages);
}

/**
 * 渲染动环实时数据表格
 */
function renderRealTimeDataTable() {
    // 获取容器
    const container = $('#realTimeDataContainer');

    // 如果没有数据，显示提示信息
    if (!realTimeData.currentPageData || realTimeData.currentPageData.length === 0) {
        container.html('<div class="loading-indicator">暂无动环实时数据</div>');
        return;
    }

    // 创建表格头部
    let html = `
    <div class="real-time-data-table">
        <div class="real-time-data-table-header">
            <div class="real-time-data-table-cell signal-name">信号名称</div>
            <div class="real-time-data-table-cell value">数值</div>
        </div>
        <div class="real-time-data-table-body">
    `;

    // 遍历当前页数据
    realTimeData.currentPageData.forEach(function(item) {
        // 格式化数值
        const formattedValue = formatRealTimeValue(item);

        // 创建行HTML
        html += `
        <div class="real-time-data-table-row">
            <div class="real-time-data-table-cell signal-name" title="${item.deviceName || '未知设备'} - ${item.signalName || '未知信号'}">
                <div class="device-name">${item.deviceName || '未知设备'}</div>
                <div class="signal-name">${item.signalName || '未知信号'}</div>
            </div>
            <div class="real-time-data-table-cell value" title="${formattedValue}">
                ${formattedValue}
            </div>
        </div>
        `;
    });

    // 关闭表格
    html += `
        </div>
    </div>
    `;

    // 更新UI
    container.html(html);
    
    console.log('%c动环实时数据表格渲染完成', 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;', 
        `渲染了 ${realTimeData.currentPageData.length} 条记录`);
}

/**
 * 格式化实时数据值
 * 参考 environment/real-time-data 页面的逻辑
 * @param {Object} item - 数据项
 * @returns {string} 格式化后的值
 */
function formatRealTimeValue(item) {
    // 如果没有valueData，返回空字符串
    if (item.valueData === undefined) return '';

    const value = parseFloat(item.valueData);

    // 处理特殊情况：describe字段包含映射信息（如"1&开 0&关"）
    if (item.describe && item.describe.includes('&')) {
        const mappings = item.describe.split(' ');

        // 查找匹配的映射
        for (const mapping of mappings) {
            const [mapValue, mapText] = mapping.split('&');
            if (parseFloat(mapValue) === value) {
                return mapText;
            }
        }
    }

    // 处理特殊情况：type=1（数字输出量/遥控）且signalName为"开机"或"关机"
    if (item.type === 1 && (item.signalName === '开机' || item.signalName === '关机')) {
        // 显示"开机"或"关机"状态
        if (item.signalName === '开机') {
            return value > 0 ? '开机' : '关机';
        } else {
            return value > 0 ? '关机' : '开机';
        }
    }

    // 默认情况：显示值+单位
    // 格式化数值，保留2位小数
    let formattedValue = item.valueData;
    if (!isNaN(value)) {
        formattedValue = value.toFixed(2);
        // 去除末尾的0和小数点
        formattedValue = formattedValue.replace(/\.?0+$/, '');
    }

    return `${formattedValue}${item.describe || ''}`;
}

/**
 * 根据信号名称获取对应的图标
 * @param {string} signalName - 信号名称
 * @returns {string} 图标HTML
 */
function getSignalIcon(signalName) {
    // 根据信号名称返回不同的图标
    if (!signalName) return '<i class="fas fa-question"></i>';

    const signalNameLower = signalName.toLowerCase();

    if (signalNameLower.includes('温度') || signalNameLower.includes('回风温度') || signalNameLower.includes('送风温度') || signalNameLower.includes('进水温度') || signalNameLower.includes('出水温度')) {
        return '<i class="fas fa-thermometer-half"></i>';
    } else if (signalNameLower.includes('湿度') || signalNameLower.includes('回风湿度')) {
        return '<i class="fas fa-tint"></i>';
    } else if (signalNameLower.includes('cpu') || signalNameLower.includes('使用率')) {
        return '<i class="fas fa-microchip"></i>';
    } else if (signalNameLower.includes('内存') || signalNameLower.includes('占用率')) {
        return '<i class="fas fa-memory"></i>';
    } else if (signalNameLower.includes('硬盘') || signalNameLower.includes('存储')) {
        return '<i class="fas fa-hdd"></i>';
    } else if (signalNameLower.includes('水阀') || signalNameLower.includes('开度')) {
        return '<i class="fas fa-faucet"></i>';
    } else {
        // 默认图标
        return '<i class="fas fa-chart-line"></i>';
    }
}

function initAnalysisChart() {
    var myChart = echarts.init(document.getElementById('analysisChart'));
    var option = {
        grid: {
            top: '10%',
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0.2)'
                }
            },
            axisLabel: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 12
            }
        },
        yAxis: {
            type: 'value',
            name: 'KW/h',
            axisLine: {
                show: false
            },
            axisLabel: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 12
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0.1)'
                }
            }
        },
        series: [{
            name: '每日能耗',
            data: [72, 68, 75, 80, 82, 76, 70],
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
                color: '#00deff',
                width: 2
            },
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgba(0,222,255,0.3)'
                }, {
                    offset: 1,
                    color: 'rgba(0,222,255,0.1)'
                }])
            }
        }]
    };
    myChart.setOption(option);
    window.addEventListener("resize", function() {
        myChart.resize();
    });
}

// 这些函数已经移动到index.html中的全局函数，这里删除以避免重复调用

})















