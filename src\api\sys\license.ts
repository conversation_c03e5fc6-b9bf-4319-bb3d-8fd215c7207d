import { defHttp } from "/@/utils/http/axios";
import { UploadFileParams } from "/#/axios";

enum Api {
  CHECK_AUTH = "/ssa/checkAuth",
  DOWNLOAD_MAC_AUTH = "/ssa/downloadMacAuth",
  UPLOAD_AUTH_FILE = "/ssa/uploadAuthFile",
}

/**
 * 授权检查响应接口（经过request过滤后的result部分）
 */
export interface LicenseCheckResult {
  time?: string; // 授权到期时间，格式：YYYY-MM-DD HH:mm:ss
}

/**
 * 授权检查接口
 * @returns Promise<LicenseCheckResult> 返回经过过滤的result部分
 */
export const checkLicense = (): Promise<LicenseCheckResult> => {
  return defHttp.get({ url: Api.CHECK_AUTH });
};

/**
 * 下载授权文件（包含mac地址的JSON格式）
 * @returns Promise<Blob>
 */
export const downloadLicense = () => {
  return defHttp.get(
    {
      url: Api.DOWNLOAD_MAC_AUTH,
      responseType: "blob",
    },
    { isTransformResponse: false }
  );
};

/**
 * 上传授权文件（文件名为jm.due，存储到jar包部署位置）
 * @param params 文件上传参数
 * @returns Promise<any>
 */
export const uploadLicense = (params: UploadFileParams) => {
  return defHttp.uploadFile(
    {
      url: Api.UPLOAD_AUTH_FILE,
    },
    params,
    {
      successMessageMode: "none", // 禁用自动成功消息提示，避免重复显示
    }
  );
};
