import { defHttp } from '/@/utils/http/axios';

// 巡检任务状态
export type InspectionStatus = 'pending' | 'in_progress' | 'completed' | 'overdue' | 'cancelled';

// 巡检类型
export type InspectionType = 'routine' | 'special' | 'emergency' | 'maintenance';

// 设备状态
export type DeviceStatus = 'normal' | 'warning' | 'error' | 'offline';

// 巡检任务
export interface InspectionTask {
  id: number;
  taskNumber: string;
  title: string;
  type: InspectionType;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: InspectionStatus;
  assignee: string;
  assigneeId: number;
  creator: string;
  creatorId: number;
  description: string;
  location: string;
  floor: string;
  area: string;
  deviceIds: number[];
  checkPoints: InspectionCheckPoint[];
  scheduledTime: string;
  startTime?: string;
  endTime?: string;
  duration?: number;
  createTime: string;
  updateTime: string;
}

// 巡检检查点
export interface InspectionCheckPoint {
  id: number;
  name: string;
  description: string;
  deviceId?: number;
  deviceName?: string;
  checkItems: InspectionCheckItem[];
  location: string;
  coordinates?: { x: number; y: number; z: number };
  qrCode?: string;
  nfcTag?: string;
  required: boolean;
  order: number;
}

// 巡检检查项
export interface InspectionCheckItem {
  id: number;
  name: string;
  description: string;
  type: 'visual' | 'measurement' | 'operation' | 'photo' | 'signature';
  required: boolean;
  standardValue?: string;
  unit?: string;
  minValue?: number;
  maxValue?: number;
  options?: string[];
  order: number;
}

// 巡检记录
export interface InspectionRecord {
  id: number;
  taskId: number;
  checkPointId: number;
  checkItemId: number;
  inspector: string;
  inspectorId: number;
  checkTime: string;
  result: 'pass' | 'fail' | 'warning';
  actualValue?: string;
  photos?: string[];
  remark?: string;
  signature?: string;
  location: string;
  coordinates?: { x: number; y: number; z: number };
}

// 巡检统计
export interface InspectionStats {
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  overdueTasks: number;
  completionRate: number;
  avgDuration: number;
  todayTasks: number;
  weekTasks: number;
  monthTasks: number;
  issueCount: number;
  deviceCount: number;
  inspectorCount: number;
}

// 巡检路线
export interface InspectionRoute {
  id: number;
  name: string;
  description: string;
  checkPoints: InspectionCheckPoint[];
  estimatedDuration: number;
  difficulty: 'easy' | 'medium' | 'hard';
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  isActive: boolean;
  createTime: string;
  updateTime: string;
}

// 查询参数
export interface InspectionQueryParams {
  pageNo?: number;
  pageSize?: number;
  taskNumber?: string;
  type?: InspectionType;
  status?: InspectionStatus;
  assignee?: string;
  location?: string;
  dateRange?: string[];
}

enum Api {
  INSPECTION_TASKS = '/operations/inspection/tasks',
  INSPECTION_STATS = '/operations/inspection/stats',
  INSPECTION_RECORDS = '/operations/inspection/records',
  INSPECTION_ROUTES = '/operations/inspection/routes',
  TASK_CREATE = '/operations/inspection/task/create',
  TASK_ASSIGN = '/operations/inspection/task/assign',
  TASK_START = '/operations/inspection/task/start',
  TASK_COMPLETE = '/operations/inspection/task/complete',
  RECORD_SUBMIT = '/operations/inspection/record/submit',
  ROUTE_CREATE = '/operations/inspection/route/create',
  QR_CODE_GENERATE = '/operations/inspection/qrcode/generate',
  DEVICE_STATUS = '/operations/inspection/device/status',
}

/**
 * 获取巡检任务列表
 */
export function getInspectionTasks(params: InspectionQueryParams) {
  return defHttp.get<{
    records: InspectionTask[];
    total: number;
    current: number;
    size: number;
  }>({
    url: Api.INSPECTION_TASKS,
    params,
  });
}

/**
 * 获取巡检统计数据
 */
export function getInspectionStats() {
  return defHttp.get<InspectionStats>({
    url: Api.INSPECTION_STATS,
  });
}

/**
 * 获取巡检记录
 */
export function getInspectionRecords(params: InspectionQueryParams) {
  return defHttp.get<{
    records: InspectionRecord[];
    total: number;
    current: number;
    size: number;
  }>({
    url: Api.INSPECTION_RECORDS,
    params,
  });
}

/**
 * 获取巡检路线
 */
export function getInspectionRoutes() {
  return defHttp.get<InspectionRoute[]>({
    url: Api.INSPECTION_ROUTES,
  });
}

/**
 * 创建巡检任务
 */
export function createInspectionTask(data: {
  title: string;
  type: InspectionType;
  priority: string;
  assigneeId: number;
  description: string;
  location: string;
  floor: string;
  area: string;
  deviceIds: number[];
  checkPointIds: number[];
  scheduledTime: string;
}) {
  return defHttp.post({
    url: Api.TASK_CREATE,
    data,
  });
}

/**
 * 分配巡检任务
 */
export function assignInspectionTask(taskId: number, assigneeId: number) {
  return defHttp.post({
    url: Api.TASK_ASSIGN,
    data: { taskId, assigneeId },
  });
}

/**
 * 开始巡检任务
 */
export function startInspectionTask(taskId: number) {
  return defHttp.post({
    url: Api.TASK_START,
    data: { taskId },
  });
}

/**
 * 完成巡检任务
 */
export function completeInspectionTask(taskId: number, summary: string) {
  return defHttp.post({
    url: Api.TASK_COMPLETE,
    data: { taskId, summary },
  });
}

/**
 * 提交巡检记录
 */
export function submitInspectionRecord(data: {
  taskId: number;
  checkPointId: number;
  checkItemId: number;
  result: string;
  actualValue?: string;
  photos?: string[];
  remark?: string;
  signature?: string;
  coordinates?: { x: number; y: number; z: number };
}) {
  return defHttp.post({
    url: Api.RECORD_SUBMIT,
    data,
  });
}

/**
 * 创建巡检路线
 */
export function createInspectionRoute(data: {
  name: string;
  description: string;
  checkPointIds: number[];
  estimatedDuration: number;
  difficulty: string;
  frequency: string;
}) {
  return defHttp.post({
    url: Api.ROUTE_CREATE,
    data,
  });
}

/**
 * 生成检查点二维码
 */
export function generateCheckPointQRCode(checkPointId: number) {
  return defHttp.get<{ qrCode: string }>({
    url: Api.QR_CODE_GENERATE,
    params: { checkPointId },
  });
}

/**
 * 获取设备状态
 */
export function getDeviceStatus(deviceIds: number[]) {
  return defHttp.post<Array<{
    deviceId: number;
    deviceName: string;
    status: DeviceStatus;
    lastCheckTime: string;
    issues: string[];
  }>>({
    url: Api.DEVICE_STATUS,
    data: { deviceIds },
  });
}
