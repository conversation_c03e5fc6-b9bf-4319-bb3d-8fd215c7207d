@charset "utf-8";
/* CSS Document */
*{
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box}
*,body{padding:0px;	margin:0px;font-family: "微软雅黑";}
body{color:#fff;font-size: 16px; background: #033c76 url(../images/bg.png); background-size:100% 100%; }
html,body{height: 100%;}
li{ list-style-type:none;}
table{}
i{ margin:0px; padding:0px; text-indent:0px;}
img{ border:none; max-width: 100%;}
a{ text-decoration:none; color:#fff;}
a.active,a:focus{ outline:none!important; text-decoration:none;}
ol,ul,p,h1,h2,h3,h4,h5,h6{ padding:0; margin:0}
a:hover{ color:#06c; text-decoration: none!important}
.clearfix:after, .clearfix:before {display: table;content: " "}
 .clearfix:after {clear: both}
.pulll_left{float:left;}
.pulll_right{float:right;}
i{font-style: normal;}
.text-w{color: #ffe400}
.text-d{color: #ff0000}
.text-s{color: #14e144}
.text-b{color: #00deff}

.head{position: relative; height: 90px; background: url(../images/topbg.png) center bottom no-repeat; background-size:100% 100%;}
.head h1{  font-size: 30px;text-align: center; line-height: 90px; color: #daf9ff;}

.head .time{position: absolute; left: 40px; line-height: 40px; top: 0; opacity: .7}
.head .name{position: absolute; right:40px; line-height: 40px; top: 0;opacity: .7}

.mainbox{padding: 0px 20px;height:calc(100vh - 90px)}
.nav1{margin-left: -20px; margin-right:-20px;}
.nav1>li{padding:0 20px; float: left;}

.box{
  position: relative;
  height:33%;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
}
.map{position:absolute; z-index: 10; opacity: .8;top: 5%; width:60%; height: 100%; left: 20%;}

.map1,.map2,.map3{ position:absolute;  top:0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;}
.map1{ z-index: 2; animation: myfirst2 15s infinite linear; display: flex;}
.map2{ z-index: 3; opacity: 0.2; animation: myfirst 10s infinite linear;}
.map3{ z-index: 1;}
.map1 img{ width: 100%}
.map2 img{width: 82%}
.map3 img{width: 80%}

.tit{ display: flex; align-items: flex-end;}
.tit span{ background: url(../images/line1.png) no-repeat bottom right; font-size: 20px; white-space: nowrap; padding-bottom: 10px; padding-right: 20px;}
.tit p{ background: url(../images/line2.png) no-repeat bottom right; width: 100%; height: 13px; margin-bottom: 5px; opacity: .5}
.boxnav{
  padding: 10px 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}
  .anchorBL,.BMap_cpyCtrl{display: none;}
#lg-counter{display: none;}



.table1 th{ border-bottom: 1px solid #407abd; font-size: 14px; padding: 10px 0; color: rgba(255,255,255,.8)}
.table1 td{ border-bottom: 1px dotted#407abd;font-size: 14px; padding:10px 0;color: rgba(255,255,255,1)}
.table1 tr:last-child td{border: none;}
.table1 td span{font-size: 12px; transform: scale(.8); display: inline-block;}
.tqdb{height: 100%;}
.tqdb li{height: 50%; float: left; width:33.333%; text-align: center;}
.tqdb li div{height:calc(100% - 25px); }
.tqdb li  h3{ font-size: 13px; color: #fff;}
.jbgc{ height: 100%; position: relative}
.jbgc li{ height: 100%; float: right;}

.jbgc li:nth-child(1){ height: 30%; position: absolute; left: 0;}


.jztxt div{padding: 20px 0 0 0; line-height: 120%;}
.jztxt div i{ font-size: 12px; color: #fff;}
.jztxt div h3{ font-size:20px; color: #00deff;font-family:electronicFont;}
.jztxt div span{ font-size: 13px; opacity: .5}
.jcjg{height: 100%; margin-left: -10px; margin-right: -10px;}
.jcjg li{width: 33.33333%;height: 100%;  float: left; height: 100%; padding: 0 10px;}
.jcjg h3{background: url(../images/tit1.png) center right no-repeat; margin-bottom: 15px;  font-weight: normal; font-size: 15px;}
.jcnav{position: relative; background: url(../images/bg1.png) no-repeat left center; height: 160px; width: 100%;}
.jcnav2{ background: url(../images/bg2.png) no-repeat left center; height: 200px; }

.jcnav img{position: absolute; left:14px; top: 50%; margin-top: -24px; }
.jcnav2 img{ left:16px; margin-top: -30px; }
.jcnavp{padding-left:98px;}
.jcnavp>div{ border: 1px solid #1070aa; display: flex; font-size: 12px; align-items: center;padding:2px;  margin-bottom: 26px; height: 36px;}
.jcnav2 .jcnavp>div{margin-bottom: 17px;}
.jcnavp>div ol{ white-space: nowrap;}
.jcnavp>div span{ padding-left:4px; color: #00e4ff; white-space: nowrap; }
.jcnavp>div i{font-size: 10px;padding-left: 2px;}


.ylfw{height: 100%; margin-left: -5px; margin-right: -5px;}
.ylfw li{width: 50%;height: 33.33333%;  float: left; padding: 0 5px;}
.ylfwbox {height:85%; border: 1px solid #1070aa; padding:10px 15px; position: relative;}
.fora{position: relative;}
.forb{position: absolute; width: 100%; left: 0; bottom: 0;}
.fora:before,.fora:after,.forb:before,.forb:after{position: absolute; content: ""; width:2px; height: 2px; border: 1px solid #00deff; opacity: .8;
box-shadow: 0 0 5px #00deff;}
.fora:before,.forb:before{left: -2px; top: -2px;}
.fora:after,.forb:after{right: -2px; top: -2px;}


.ylfwbox p{ font-size: 13px;}
.ylfwbox ol{ }
.ylfwbox ol i{ font-size: 12px;}
.ylfwbox ol em{ font-size: 14px; font-style: normal; padding: 0 5px;}
.ylfwbox ol span{ font-size: 24px;  color: #00deff; text-shadow: 0 0 5px #00deff;font-family:electronicFont;}

.drqk{height: 100%;}
.drqk li{height: 33.33333333%; width: 50%; float: left;
display: flex;align-items: center;
}
/* 设备运行状态项样式 */
.drqk li.data-item {
  height: 33.33333333%;
  width: 100%;
  float: left;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 数量统计行样式 */
.stats-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  padding: 0 10px;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 33.33%;
  text-align: center;
  padding: 10px 5px;
  transition: all 0.3s ease;
}

.stats-item:hover {
  transform: translateY(-3px);
}

.stats-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 8px;
}

.stats-content span {
  opacity: 0.7;
  font-size: 14px;
  margin-bottom: 5px;
  color: rgba(255, 255, 255, 0.9);
}

.stats-content p {
  margin: 0;
  line-height: 1.2;
}

.stats-content em {
  font-size: 26px;
  color: #00deff;
  text-shadow: 0 0 5px #00deff;
  font-family: electronicFont;
  font-style: normal;
}

.stats-content i {
  font-size: 14px;
  padding-left: 5px;
  font-style: normal;
}

/* Ant Design 图标样式 */
.ant-icon {
  background: url(../images/iconbg.png);
  width: 63px;
  height: 63px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  color: #fff;
  font-size: 28px;
}

.ant-icon svg {
  width: 32px;
  height: 32px;
  fill: #00deff;
  filter: drop-shadow(0 0 3px rgba(0, 222, 255, 0.7));
}

/* 迷你版图标样式 */
.ant-icon-mini {
  background: url(../images/iconbg.png);
  background-size: cover;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 24px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.ant-icon-mini:hover {
  transform: rotate(10deg);
}

.ant-icon-mini svg {
  width: 28px;
  height: 28px;
  fill: #00deff;
  filter: drop-shadow(0 0 5px rgba(0, 222, 255, 0.8));
}
@font-face{font-family:electronicFont;src:url(../font/DS-DIGIT.TTF)}
.icon{ background: url(../images/iconbg.png);background-size:100% 100%; width:43px; height: 43px; display: flex; justify-content: center;align-items: center}
.drqk li span{opacity: .5; font-size:15px;}
.drqk li i{ padding-left: 10px;}
.drqk li em{background: linear-gradient( 0deg,#45d3fd, #45d3fd, #61ddb1,#61ddb1); font-style:normal;
    background-size: cover;font-family:electronicFont;
	font-size: 30px;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;}



@keyframes myfirst2
{
from {transform: rotate(0deg);}
to {transform: rotate(359deg);}
}

@keyframes myfirst
{
from {transform: rotate(0deg);}
to {transform: rotate(-359deg);}
}

.data-total {
  height: 100%;
  padding: 20px;
  background: rgba(3,43,123,0.3);
  border: 1px solid #1070aa;
}

.total-grid {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 15px;
}

.grid-item {
  background: rgba(3,43,123,0.3);
  border: 1px solid #1070aa;
  padding: 15px;
  position: relative;
}

.grid-item h3 {
  color: rgba(255,255,255,0.8);
  font-size: 15px;
  margin-bottom: 15px;
  background: url(../images/tit1.png) center right no-repeat;
  font-weight: normal;
}

.data-box {
  display: flex;
  justify-content: space-around;
}

.data-item {
  position: relative;
  text-align: center;
  padding: 10px;
}

.data-item .label {
  display: block;
  font-size: 13px;
  color: rgba(255,255,255,0.5);
  margin-bottom: 8px;
}

.data-item .value {
  font-family: electronicFont;
  font-size: 24px;
  color: #00deff;
  text-shadow: 0 0 5px #00deff;
}

.data-item .unit {
  font-size: 12px;
  color: #fff;
  margin-left: 3px;
}

.trend {
  font-size: 12px;
  margin-left: 5px;
}

.trend.up {
  color: #14e144;
}

.trend.down {
  color: #ff0000;
}

.area-list {
  padding: 10px 0;
}

.area-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  border: 1px solid #1070aa;
  padding: 8px 12px;
}

.area-item .name {
  font-size: 14px;
  color: rgba(255,255,255,0.5);
  margin-right: 10px;
}

.area-item .progress {
  flex: 1;
  height: 6px;
  background: rgba(255,255,255,0.1);
  border-radius: 3px;
  margin: 0 10px;
}

.area-item .progress .bar {
  height: 100%;
  background: linear-gradient(90deg, #00cefc 0%, #367bec 100%);
  border-radius: 3px;
}

.area-item .percent {
  font-size: 14px;
  color: #00e4ff;
  white-space: nowrap;
}

/* 机房列表样式 */
.rooms-list {
  margin-top: 10px;
  padding: 10px;
  background: rgba(3,43,123,0.3);
  border: 1px solid #1070aa;
}

.rooms-list h3 {
  color: rgba(255,255,255,0.8);
  font-size: 15px;
  margin-bottom: 15px;
  background: url(../images/tit1.png) center right no-repeat;
  font-weight: normal;
}

.room-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.room-item {
  width: calc(50% - 5px);
  padding: 10px;
  background: rgba(3,60,118,0.5);
  border: 1px solid #1070aa;
  position: relative;
}

.room-name {
  font-size: 14px;
  color: #fff;
  margin-bottom: 8px;
}

.room-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.room-status {
  padding: 2px 6px;
  border-radius: 2px;
}

.status-normal {
  background: rgba(20,225,68,0.2);
  color: #14e144;
}

.status-warning {
  background: rgba(255,0,0,0.2);
  color: #ff0000;
}

.room-devices {
  color: rgba(255,255,255,0.7);
}

/* 机房详情样式 */
.room-detail {
  margin-top: 10px;
  padding: 15px;
  background: rgba(3,43,123,0.3);
  border: 1px solid #1070aa;
}

.room-detail h3 {
  color: rgba(255,255,255,0.8);
  font-size: 15px;
  margin-bottom: 15px;
  background: url(../images/tit1.png) center right no-repeat;
  font-weight: normal;
}

.detail-item {
  display: flex;
  margin-bottom: 10px;
  padding: 5px 0;
  border-bottom: 1px dotted #407abd;
}

.detail-label {
  width: 40%;
  color: rgba(255,255,255,0.5);
  font-size: 13px;
}

.detail-value {
  width: 60%;
  color: #00e4ff;
  font-size: 13px;
}

/* 动环列表样式 */
.environment-list-container {
  height: 100%;
  position: relative;
  padding: 2px;
  background: rgba(3,43,123,0.3);
  border: 1px solid #1070aa;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.environment-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: #1070aa rgba(3,43,123,0.3);
  padding: 1px;
  max-height: 100%;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

.environment-list::-webkit-scrollbar {
  width: 6px;
}

.environment-list::-webkit-scrollbar-track {
  background: rgba(3,43,123,0.3);
  border-radius: 3px;
}

.environment-list::-webkit-scrollbar-thumb {
  background-color: #1070aa;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.environment-item {
  padding: 8px 10px;
  margin: 0 2px 6px 2px;
  background: rgba(3,60,118,0.5);
  border: 1px solid #1070aa;
  position: relative;
  transition: all 0.2s ease;
  border-radius: 4px;
  box-sizing: border-box;
  width: calc(100% - 4px); /* 确保宽度不超出容器 */
  max-width: 100%;
}

.environment-item:hover {
  background: rgba(3,60,118,0.8);
  border-color: #00deff;
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(0, 222, 255, 0.2);
}

.environment-item:last-child {
  margin-bottom: 6px;
}

.environment-name {
  font-size: 14px;
  color: #fff;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  display: block;
}

.loading-indicator {
  text-align: center;
  padding: 20px;
  color: rgba(255,255,255,0.7);
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  position: relative;
}

.loading-indicator::after {
  content: "...";
  position: absolute;
  animation: loading-dots 1.5s infinite;
  margin-left: 5px;
}

@keyframes loading-dots {
  0% { content: "."; }
  33% { content: ".."; }
  66% { content: "..."; }
}

/* 以下样式已不再使用，但保留注释以便将来可能需要时参考
.environment-info {
  display: flex;
  flex-wrap: wrap;
  font-size: 11px;
  color: rgba(255,255,255,0.7);
  margin-bottom: 2px;
}

.environment-info-item {
  width: 50%;
  margin-bottom: 2px;
  display: flex;
  overflow: hidden;
}

.environment-info-label {
  color: rgba(255,255,255,0.5);
  margin-right: 4px;
  flex-shrink: 0;
}

.environment-info-value {
  color: #00e4ff;
  max-width: calc(100% - 45px);
}

.environment-desc {
  margin-top: 2px;
  font-size: 11px;
  color: rgba(255,255,255,0.6);
  font-style: italic;
  padding-left: 3px;
  border-left: 1px solid #1070aa;
  max-width: 100%;
}
*/

/* 文本截断样式 */
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
}

/* 鼠标悬停提示样式 */
[title] {
  cursor: help;
  position: relative;
}

/* 响应式设计 - 确保在不同屏幕尺寸下正常显示 */
@media screen and (max-width: 1366px) {
  .environment-item {
    padding: 6px 8px;
    margin-bottom: 4px;
  }

  .environment-name {
    font-size: 13px;
  }
}

@media screen and (max-width: 1024px) {
  .environment-item {
    padding: 5px 6px;
    margin-bottom: 3px;
  }

  .environment-name {
    font-size: 12px;
  }

  .environment-list::-webkit-scrollbar {
    width: 4px;
  }
}

/* 确保在高DPI屏幕上显示清晰 */
@media (-webkit-min-device-pixel-ratio: 1.5), (min-resolution: 144dpi) {
  .environment-item {
    border-width: 0.5px;
  }

  .environment-list::-webkit-scrollbar {
    width: 5px;
  }
}

