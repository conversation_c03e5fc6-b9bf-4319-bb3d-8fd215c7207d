import { defHttp } from '/@/utils/http/axios';

enum Api {
  WaterMeterList = '/baWatermeter',
  WaterMeterHistory = '/baWatermeterHistory',
  YesterdayWater = '/baWatermeter/yesterdayWater',
}

// 水表基本信息接口
export interface WaterMeterInfo {
  id: string;
  name: string;
  code: string;
  type: string;
  remark: string;
  dataTime: string;
  valueData: string;
  addr: string;
}

// 水表历史数据查询参数接口
export interface WaterMeterHistoryParams {
  waterId?: string; // 水表ID
  day?: number; // 查询天数
}

/**
 * 获取水表实时数据
 * @returns 返回水表列表数据
 */
export const getWaterMeterList = (): Promise<WaterMeterInfo[]> => {
  return defHttp.get({ url: Api.WaterMeterList });
};

/**
 * 获取水表历史数据
 * @param params 查询参数，包含 waterId（水表ID）和 day（查询天数）
 * @returns 返回水表历史数据，按水表名称分组
 */
export const getWaterMeterHistory = (params?: WaterMeterHistoryParams): Promise<Record<string, any[]>> => {
  // 过滤掉 undefined 值的参数
  const filteredParams = params ? Object.fromEntries(Object.entries(params).filter(([_, value]) => value !== undefined)) : undefined;

  return defHttp.get({ url: Api.WaterMeterHistory, params: filteredParams });
};

/**
 * 获取昨日用水量
 */
export const getYesterdayWater = () => {
  return defHttp.get({ url: Api.YesterdayWater });
};
