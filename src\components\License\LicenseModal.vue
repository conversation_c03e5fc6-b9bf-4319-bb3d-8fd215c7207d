<template>
  <a-modal
    v-model:open="visible"
    title="授权管理"
    width="650px"
    :footer="null"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <div class="license-modal p-6">
      <!-- 授权状态显示 -->
      <div class="license-status mb-8">
        <div class="flex items-center mb-6">
          <div class="flex items-center">
            <div
              :class="[
                'w-4 h-4 rounded-full mr-3',
                licenseInfo.isValid ? 'bg-green-500' : 'bg-red-500',
              ]"
            ></div>
            <span class="text-xl font-semibold">
              {{ licenseInfo.isValid ? "授权有效" : "授权无效" }}
            </span>
          </div>
        </div>

        <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <div class="grid grid-cols-1 gap-4 text-sm">
            <div v-if="licenseInfo.expiryTime" class="flex justify-between items-center">
              <span class="text-gray-600 font-medium">到期时间：</span>
              <span class="font-semibold text-gray-800">{{ licenseInfo.expiryTime }}</span>
            </div>
            <div
              v-if="licenseInfo.remainingDays !== undefined"
              class="flex justify-between items-center"
            >
              <span class="text-gray-600 font-medium">剩余天数：</span>
              <span :class="['font-semibold', getRemainingDaysColor(licenseInfo.remainingDays)]">
                {{ licenseInfo.remainingDays > 0 ? `${licenseInfo.remainingDays} 天` : "已过期" }}
              </span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600 font-medium">状态说明：</span>
              <span
                :class="['font-semibold', licenseInfo.isValid ? 'text-green-600' : 'text-red-600']"
              >
                {{ licenseInfo.message }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 授权无效时的提示信息 -->
      <div v-if="!licenseInfo.isValid" class="mb-6 p-4 bg-red-50 rounded-lg border border-red-200">
        <div class="flex items-start">
          <div class="flex-shrink-0 mr-3 mt-1">
            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
              <span class="text-red-600 text-sm font-bold">!</span>
            </div>
          </div>
          <div class="flex-1">
            <h4 class="text-red-800 font-semibold mb-2">系统授权已失效</h4>
            <div class="text-red-700 text-sm leading-relaxed space-y-1">
              <p>• 请下载当前系统的授权文件</p>
              <p>• 将下载的文件发送给服务商进行授权</p>
              <p>• 获得新的授权文件后，通过下方上传功能更新授权</p>
              <p>• 如有疑问，请联系技术支持</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件操作区域 - 始终显示 -->
      <div class="license-actions">
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-4 text-gray-800">授权文件管理</h4>

          <!-- 下载授权文件 -->
          <div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-800 mb-1">下载授权文件</div>
                <div class="text-sm text-gray-600">
                  {{
                    licenseInfo.isValid
                      ? "下载当前系统的授权文件进行备份"
                      : "下载授权文件发送给服务商进行授权申请"
                  }}
                </div>
              </div>
              <a-button type="primary" :loading="downloadLoading" @click="handleDownload">
                <template #icon>
                  <DownloadOutlined />
                </template>
                下载文件
              </a-button>
            </div>
          </div>

          <!-- 上传新授权文件 -->
          <div class="p-4 bg-green-50 rounded-lg border border-green-200">
            <div class="mb-4">
              <div class="font-medium text-gray-800 mb-1">上传新授权文件</div>
              <div class="text-sm text-gray-600">
                {{
                  licenseInfo.isValid
                    ? "上传新的授权文件以更新系统授权"
                    : "上传从服务商获得的新授权文件"
                }}
              </div>
            </div>

            <div class="mb-4">
              <a-upload
                :file-list="fileList"
                :before-upload="beforeUpload"
                :remove="handleRemove"
                accept=".due"
                :multiple="false"
                :max-count="1"
                class="upload-area"
              >
                <a-button :loading="uploadLoading" size="large">
                  <template #icon>
                    <UploadOutlined />
                  </template>
                  选择授权文件
                </a-button>
              </a-upload>
            </div>

            <div class="text-xs text-gray-500 mb-3">
              支持 .due 格式的授权文件，文件大小不超过 10MB
            </div>

            <div v-if="fileList.length > 0" class="flex justify-end">
              <a-button type="primary" :loading="uploadLoading" @click="handleUpload" size="large">
                <template #icon>
                  <UploadOutlined />
                </template>
                上传授权文件
              </a-button>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-between items-center pt-6 border-t border-gray-200">
          <a-button type="default" :loading="checkLoading" @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新状态
          </a-button>

          <a-button @click="handleCancel" size="large"> 关闭 </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { message } from "ant-design-vue";
import { DownloadOutlined, UploadOutlined, ReloadOutlined } from "@ant-design/icons-vue";
import { downloadLicense, uploadLicense } from "/@/api/sys/license";
import { LicenseInfo } from "/@/types/license";
import type { UploadFile } from "ant-design-vue";

interface Props {
  open: boolean;
  licenseInfo: LicenseInfo;
}

interface Emits {
  (e: "update:open", value: boolean): void;
  (e: "refresh"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const downloadLoading = ref(false);
const uploadLoading = ref(false);
const checkLoading = ref(false);
const fileList = ref<UploadFile[]>([]);

// 监听props变化
watch(
  () => props.open,
  (newVal) => {
    visible.value = newVal;
  },
  { immediate: true }
);

watch(visible, (newVal) => {
  emit("update:open", newVal);
});

/**
 * 获取剩余天数的颜色样式
 */
const getRemainingDaysColor = (days: number): string => {
  if (days <= 0) return "text-red-600";
  if (days <= 7) return "text-red-500";
  if (days <= 30) return "text-orange-500";
  return "text-green-600";
};

/**
 * 下载授权文件
 */
const handleDownload = async () => {
  try {
    downloadLoading.value = true;
    const response = await downloadLicense();

    // 检查响应是否为Blob类型
    if (!(response instanceof Blob)) {
      throw new Error("下载响应格式错误");
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(response);
    const link = document.createElement("a");
    link.href = url;
    link.download = "license.due";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    message.success("授权文件下载成功");
  } catch (error) {
    console.error("下载授权文件失败:", error);
    message.error("下载授权文件失败，请稍后重试");
  } finally {
    downloadLoading.value = false;
  }
};

/**
 * 文件上传前的处理
 */
const beforeUpload = (file: UploadFile) => {
  const isValidType = file.name?.toLowerCase().endsWith(".due");

  if (!isValidType) {
    message.error("请选择有效的授权文件格式（.due）");
    return false;
  }

  const isLt10M = (file.size || 0) / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error("授权文件大小不能超过 10MB");
    return false;
  }

  fileList.value = [file];
  return false; // 阻止自动上传
};

/**
 * 移除文件
 */
const handleRemove = () => {
  fileList.value = [];
};

/**
 * 上传授权文件
 */
const handleUpload = async () => {
  if (fileList.value.length === 0) {
    message.warning("请先选择授权文件");
    return;
  }

  try {
    uploadLoading.value = true;
    const file = fileList.value[0];

    // 确保文件对象正确转换
    const uploadFile = file.originFileObj || file;

    await uploadLicense({
      file: uploadFile as File,
      name: "file",
    });

    fileList.value = [];

    // 上传成功后刷新授权状态
    emit("refresh");
  } catch (error) {
    console.error("上传授权文件失败:", error);
    message.error("上传授权文件失败，请检查文件格式是否正确");
  } finally {
    uploadLoading.value = false;
  }
};

/**
 * 刷新授权状态
 */
const handleRefresh = () => {
  emit("refresh");
};

/**
 * 关闭模态框
 */
const handleCancel = () => {
  visible.value = false;
  fileList.value = [];
};
</script>

<style lang="less" scoped>
.license-modal {
  .license-status {
    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;

      &.valid {
        background-color: #52c41a;
      }

      &.invalid {
        background-color: #ff4d4f;
      }
    }
  }

  .upload-area {
    :deep(.ant-upload) {
      width: 100%;
    }

    :deep(.ant-btn) {
      width: 100%;
      height: 48px;
      border-style: dashed;
      border-width: 2px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #40a9ff;
        background-color: #f0f8ff;
      }
    }
  }

  .bg-blue-50 {
    transition: all 0.3s ease;

    &:hover {
      background-color: #e6f7ff;
      border-color: #91d5ff;
    }
  }

  .bg-green-50 {
    transition: all 0.3s ease;

    &:hover {
      background-color: #f6ffed;
      border-color: #b7eb8f;
    }
  }
}
</style>
