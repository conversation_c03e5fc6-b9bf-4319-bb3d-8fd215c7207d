import { defHttp } from '/@/utils/http/axios';

// 访客信息接口
export interface VisitorInfo {
  id: number;
  name: string;
  phone: string;
  idCard: string;
  company: string;
  visitPurpose: string;
  visitee: string;
  visiteePhone: string;
  visitTime: string;
  expectedLeaveTime: string;
  actualLeaveTime?: string;
  status: 'pending' | 'approved' | 'rejected' | 'visiting' | 'completed';
  qrCode?: string;
  photo?: string;
  temperature?: number;
  healthCode?: string;
  createTime: string;
  updateTime: string;
}

// 访客统计数据
export interface VisitorStats {
  todayTotal: number;
  todayVisiting: number;
  weekTotal: number;
  monthTotal: number;
  pendingApproval: number;
  avgVisitDuration: number;
}

// 访客预约参数
export interface VisitorAppointmentParams {
  name: string;
  phone: string;
  idCard: string;
  company: string;
  visitPurpose: string;
  visitee: string;
  visiteePhone: string;
  visitTime: string;
  expectedLeaveTime: string;
  photo?: string;
}

// 访客查询参数
export interface VisitorQueryParams {
  pageNo?: number;
  pageSize?: number;
  name?: string;
  phone?: string;
  company?: string;
  status?: string;
  visitTime?: string[];
}

enum Api {
  VISITOR_LIST = '/operations/visitor/list',
  VISITOR_STATS = '/operations/visitor/stats',
  VISITOR_APPOINTMENT = '/operations/visitor/appointment',
  VISITOR_APPROVE = '/operations/visitor/approve',
  VISITOR_CHECKIN = '/operations/visitor/checkin',
  VISITOR_CHECKOUT = '/operations/visitor/checkout',
  VISITOR_QR_CODE = '/operations/visitor/qrcode',
  VISITOR_DELETE = '/operations/visitor/delete',
}

/**
 * 获取访客列表
 */
export function getVisitorList(params: VisitorQueryParams) {
  return defHttp.get<{
    records: VisitorInfo[];
    total: number;
    current: number;
    size: number;
  }>({
    url: Api.VISITOR_LIST,
    params,
  });
}

/**
 * 获取访客统计数据
 */
export function getVisitorStats() {
  return defHttp.get<VisitorStats>({
    url: Api.VISITOR_STATS,
  });
}

/**
 * 访客预约
 */
export function createVisitorAppointment(data: VisitorAppointmentParams) {
  return defHttp.post({
    url: Api.VISITOR_APPOINTMENT,
    data,
  });
}

/**
 * 审批访客申请
 */
export function approveVisitor(id: number, approved: boolean, remark?: string) {
  return defHttp.post({
    url: Api.VISITOR_APPROVE,
    data: { id, approved, remark },
  });
}

/**
 * 访客签到
 */
export function visitorCheckIn(id: number, temperature: number, healthCode: string) {
  return defHttp.post({
    url: Api.VISITOR_CHECKIN,
    data: { id, temperature, healthCode },
  });
}

/**
 * 访客签退
 */
export function visitorCheckOut(id: number) {
  return defHttp.post({
    url: Api.VISITOR_CHECKOUT,
    data: { id },
  });
}

/**
 * 生成访客二维码
 */
export function generateVisitorQRCode(id: number) {
  return defHttp.get<{ qrCode: string }>({
    url: Api.VISITOR_QR_CODE,
    params: { id },
  });
}

/**
 * 删除访客记录
 */
export function deleteVisitor(id: number) {
  return defHttp.delete({
    url: Api.VISITOR_DELETE,
    params: { id },
  });
}
