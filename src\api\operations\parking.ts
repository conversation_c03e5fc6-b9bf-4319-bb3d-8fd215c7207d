import { defHttp } from '/@/utils/http/axios';

// 停车位信息
export interface ParkingSpace {
  id: number;
  spaceNumber: string;
  floor: string;
  area: string;
  type: 'normal' | 'vip' | 'disabled' | 'electric';
  status: 'available' | 'occupied' | 'reserved' | 'maintenance';
  vehicleNumber?: string;
  ownerName?: string;
  ownerPhone?: string;
  parkTime?: string;
  expectedLeaveTime?: string;
  fee?: number;
  coordinates?: { x: number; y: number };
}

// 停车统计数据
export interface ParkingStats {
  totalSpaces: number;
  occupiedSpaces: number;
  availableSpaces: number;
  reservedSpaces: number;
  occupancyRate: number;
  todayIncome: number;
  monthlyIncome: number;
  avgParkingDuration: number;
  peakHours: string[];
}

// 车辆进出记录
export interface VehicleRecord {
  id: number;
  vehicleNumber: string;
  ownerName?: string;
  ownerPhone?: string;
  entryTime: string;
  exitTime?: string;
  parkingDuration?: number;
  fee: number;
  paymentStatus: 'unpaid' | 'paid' | 'free';
  paymentMethod?: 'cash' | 'card' | 'mobile' | 'monthly';
  spaceNumber: string;
  floor: string;
  area: string;
}

// 停车引导信息
export interface ParkingGuidance {
  floor: string;
  area: string;
  availableSpaces: number;
  recommendedSpaces: ParkingSpace[];
  estimatedWalkTime: number;
  route?: string;
}

// 查询参数
export interface ParkingQueryParams {
  pageNo?: number;
  pageSize?: number;
  floor?: string;
  area?: string;
  status?: string;
  vehicleNumber?: string;
  dateRange?: string[];
}

enum Api {
  PARKING_SPACES = '/operations/parking/spaces',
  PARKING_STATS = '/operations/parking/stats',
  VEHICLE_RECORDS = '/operations/parking/records',
  PARKING_GUIDANCE = '/operations/parking/guidance',
  VEHICLE_ENTRY = '/operations/parking/entry',
  VEHICLE_EXIT = '/operations/parking/exit',
  SPACE_RESERVE = '/operations/parking/reserve',
  PAYMENT_PROCESS = '/operations/parking/payment',
  SPACE_MAINTENANCE = '/operations/parking/maintenance',
}

/**
 * 获取停车位列表
 */
export function getParkingSpaces(params: ParkingQueryParams) {
  return defHttp.get<{
    records: ParkingSpace[];
    total: number;
    current: number;
    size: number;
  }>({
    url: Api.PARKING_SPACES,
    params,
  });
}

/**
 * 获取停车统计数据
 */
export function getParkingStats() {
  return defHttp.get<ParkingStats>({
    url: Api.PARKING_STATS,
  });
}

/**
 * 获取车辆进出记录
 */
export function getVehicleRecords(params: ParkingQueryParams) {
  return defHttp.get<{
    records: VehicleRecord[];
    total: number;
    current: number;
    size: number;
  }>({
    url: Api.VEHICLE_RECORDS,
    params,
  });
}

/**
 * 获取停车引导信息
 */
export function getParkingGuidance(vehicleType: string = 'normal') {
  return defHttp.get<ParkingGuidance[]>({
    url: Api.PARKING_GUIDANCE,
    params: { vehicleType },
  });
}

/**
 * 车辆入场
 */
export function vehicleEntry(data: {
  vehicleNumber: string;
  ownerName?: string;
  ownerPhone?: string;
  spaceNumber?: string;
}) {
  return defHttp.post({
    url: Api.VEHICLE_ENTRY,
    data,
  });
}

/**
 * 车辆出场
 */
export function vehicleExit(vehicleNumber: string) {
  return defHttp.post({
    url: Api.VEHICLE_EXIT,
    data: { vehicleNumber },
  });
}

/**
 * 预约停车位
 */
export function reserveParkingSpace(data: {
  spaceNumber: string;
  vehicleNumber: string;
  ownerName: string;
  ownerPhone: string;
  reserveTime: string;
  expectedDuration: number;
}) {
  return defHttp.post({
    url: Api.SPACE_RESERVE,
    data,
  });
}

/**
 * 处理停车费用
 */
export function processPayment(data: {
  recordId: number;
  paymentMethod: string;
  amount: number;
}) {
  return defHttp.post({
    url: Api.PAYMENT_PROCESS,
    data,
  });
}

/**
 * 设置停车位维护状态
 */
export function setSpaceMaintenance(spaceId: number, maintenance: boolean, reason?: string) {
  return defHttp.post({
    url: Api.SPACE_MAINTENANCE,
    data: { spaceId, maintenance, reason },
  });
}
